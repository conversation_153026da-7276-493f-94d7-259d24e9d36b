import React, { useState, useEffect } from 'react';
import {
  Table as AntTable,
  Space,
  Tag,
  message,
  Modal,
  Empty,
  Pagination,
  Spin,
  DatePicker,
  Switch,
} from 'antd';
import {
  ReloadOutlined,
  PlusOutlined,
  CalendarOutlined,
  PercentageOutlined,
  DollarOutlined,
  GiftOutlined,
} from '@ant-design/icons';
import { useFetchClient } from '@strapi/helper-plugin';
import {
  PageContainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  StatsGrid,
  StatsCard,
  Button,
  FilterGroup,
  FilterLabel,
  SelectInput,
  ActionButtonGroup,
  StyledTable,
} from '../components/shared';
import { Gift, DollarSign, TrendingUp, Calendar } from 'lucide-react';

import PromotionModal from '../components/PromotionModal';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

const PromotionManagement = () => {
  const { get, put, del } = useFetchClient();

  const [promotions, setPromotions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState(null);
  const [status, setStatus] = useState('');
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [dateRange, setDateRange] = useState(null);

  // Modal states
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPromotion, setEditingPromotion] = useState(null);

  const PAGE_SIZE = 10;

  // Stats data for display
  const statsData = [
    {
      title: 'Tổng khuyến mãi',
      value: statistics?.totalPromotions || 0,
      icon: Gift,
      color: '#3b82f6',
    },
    {
      title: 'Đang hoạt động',
      value: statistics?.activePromotions || 0,
      icon: TrendingUp,
      color: '#10b981',
    },
    {
      title: 'Đã hết hạn',
      value: statistics?.expiredPromotions || 0,
      icon: Calendar,
      color: '#f59e0b',
    },
    {
      title: 'Sắp diễn ra',
      value: statistics?.upcomingPromotions || 0,
      icon: DollarSign,
      color: '#8b5cf6',
    },
  ];

  const fetchPromotions = async (
    currentPage = page,
    currentSearch = search,
    currentStatus = status,
    currentDateRange = dateRange
  ) => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: PAGE_SIZE.toString(),
        ...(currentSearch && { search: currentSearch }),
        ...(currentStatus && { status: currentStatus }),
        ...(currentDateRange &&
          currentDateRange[0] && {
            startDate: currentDateRange[0].format('YYYY-MM-DD'),
          }),
        ...(currentDateRange &&
          currentDateRange[1] && {
            endDate: currentDateRange[1].format('YYYY-MM-DD'),
          }),
      });

      const response = await get(`/management/promotions?${queryParams}`);

      console.log('Promotion response:', response); // Debug log
      setPromotions(response.data.data.data ?? []);
      setTotal(response.data?.meta?.pagination?.total || 0);
    } catch (error) {
      console.error('Error fetching promotions:', error);
      message.error('Không thể tải danh sách khuyến mãi');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await get('/management/promotions/statistics');
      console.log('Statistics response:', response); // Debug log
      // The backend returns { success: true, data: { ... } }
      setStatistics(response.data?.data || null);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  // Handle search
  const handleSearch = (value) => {
    setSearch(value);
    setPage(1);
  };

  // Handle filter change
  const handleFilterChange = (filterType, value) => {
    if (filterType === 'status') {
      setStatus(value);
    }
    setPage(1);
  };

  // Handle date range change
  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
    setPage(1);
  };

  // Handle add promotion
  const handleAdd = () => {
    setEditingPromotion(null);
    setModalVisible(true);
  };

  // Handle edit promotion
  const handleEdit = (promotion) => {
    setEditingPromotion(promotion);
    setModalVisible(true);
  };

  // Handle delete promotion
  const handleDelete = async (promotion) => {
    try {
      await del(`/management/promotions/${promotion.id}`);
      message.success('Xóa khuyến mãi thành công');
      fetchPromotions();
      fetchStatistics();
    } catch (error) {
      console.error('Error deleting promotion:', error);
      message.error('Không thể xóa khuyến mãi');
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (promotion) => {
    try {
      await put(`/management/promotions/${promotion.id}/status`, {
        isActive: !promotion.isActive,
      });
      message.success(
        `${promotion.isActive ? 'Tắt' : 'Bật'} khuyến mãi thành công`
      );
      fetchPromotions();
      fetchStatistics();
    } catch (error) {
      console.error('Error toggling promotion status:', error);
      message.error('Không thể cập nhật trạng thái khuyến mãi');
    }
  };

  // Handle modal success
  const handleModalSuccess = () => {
    setModalVisible(false);
    setEditingPromotion(null);
    fetchPromotions();
    fetchStatistics();
  };

  // Handle modal cancel
  const handleModalCancel = () => {
    setModalVisible(false);
    setEditingPromotion(null);
  };

  const columns = [
    {
      title: 'Khuyến mãi',
      key: 'promotion',
      width: 300,
      render: (record) => (
        <div>
          <div
            style={{
              fontWeight: 600,
              color: '#1e293b',
              fontSize: '14px',
              marginBottom: '4px',
            }}
          >
            {record.name}
          </div>
          <div
            style={{ fontSize: '12px', color: '#64748b', marginBottom: '4px' }}
          >
            {record.description}
          </div>
          <Space size={4}>
            <Tag color="blue" style={{ fontSize: '11px' }}>
              {record.code}
            </Tag>
            {record.isPublic && (
              <Tag color="green" style={{ fontSize: '11px' }}>
                Công khai
              </Tag>
            )}
          </Space>
        </div>
      ),
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Loại & Giá trị',
      key: 'type',
      width: 180,
      render: (record) => {
        const typeConfig = {
          percentage: {
            text: 'Phần trăm',
            icon: <PercentageOutlined />,
            color: '#3b82f6',
          },
          fixed_amount: {
            text: 'Số tiền cố định',
            icon: <DollarOutlined />,
            color: '#10b981',
          },
          free_shipping: {
            text: 'Miễn phí ship',
            icon: <GiftOutlined />,
            color: '#f59e0b',
          },
        };
        const config = typeConfig[record.type];

        return (
          <div>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 4,
                marginBottom: '4px',
                color: config.color,
                fontSize: '12px',
                fontWeight: 500,
              }}
            >
              {config.icon}
              {config.text}
            </div>
            <div
              style={{
                fontWeight: 600,
                color: '#1e293b',
                fontSize: '14px',
              }}
            >
              {record.type === 'percentage'
                ? `${record.value}%`
                : record.type === 'fixed_amount'
                ? new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND',
                  }).format(record.value)
                : record.type === 'free_shipping'
                ? 'Miễn phí'
                : `${record.value}`}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Điều kiện',
      key: 'conditions',
      width: 200,
      render: (record) => (
        <div>
          <div
            style={{ fontSize: '12px', color: '#64748b', marginBottom: '2px' }}
          >
            Đơn tối thiểu:{' '}
            {new Intl.NumberFormat('vi-VN', {
              style: 'currency',
              currency: 'VND',
            }).format(record.minOrderAmount)}
          </div>
          {record.maxDiscountAmount && (
            <div
              style={{
                fontSize: '12px',
                color: '#64748b',
                marginBottom: '2px',
              }}
            >
              Giảm tối đa:{' '}
              {new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND',
              }).format(record.maxDiscountAmount)}
            </div>
          )}
          <div style={{ fontSize: '12px', color: '#64748b' }}>
            Sử dụng: {record.usageCount}/{record.usageLimit || '∞'}
          </div>
        </div>
      ),
    },
    {
      title: 'Thời gian',
      key: 'time',
      width: 180,
      render: (record) => {
        const now = new Date();
        const startDate = new Date(record.startDate);
        const endDate = new Date(record.endDate);
        const isExpired = now > endDate;
        const isUpcoming = now < startDate;

        return (
          <div>
            <div
              style={{
                fontSize: '12px',
                color: '#64748b',
                marginBottom: '2px',
              }}
            >
              <CalendarOutlined style={{ marginRight: 4 }} />
              {startDate.toLocaleDateString('vi-VN')} -{' '}
              {endDate.toLocaleDateString('vi-VN')}
            </div>
            <Tag
              color={isExpired ? 'red' : isUpcoming ? 'orange' : 'green'}
              style={{ fontSize: '11px' }}
            >
              {isExpired
                ? 'Đã hết hạn'
                : isUpcoming
                ? 'Sắp diễn ra'
                : 'Đang diễn ra'}
            </Tag>
          </div>
        );
      },
      sorter: (a, b) =>
        new Date(a.startDate).getTime() - new Date(b.startDate).getTime(),
    },
    {
      title: 'Trạng thái',
      key: 'status',
      width: 120,
      render: (record) => (
        <Switch
          checked={record.isActive}
          onChange={() => handleToggleStatus(record)}
          size="small"
        />
      ),
      filters: [
        { text: 'Hoạt động', value: 'active' },
        { text: 'Tạm dừng', value: 'inactive' },
      ],
      onFilter: (value, record) =>
        value === 'active' ? record.isActive : !record.isActive,
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      render: (record) => (
        <ActionButtonGroup
          onEdit={() => handleEdit(record)}
          onDelete={() => handleDelete(record)}
          deleteConfirmTitle="Xóa khuyến mãi"
          deleteConfirmDescription={`Bạn có chắc chắn muốn xóa khuyến mãi "${record.name}"?`}
          showView={false}
          showEdit={true}
          showDelete={true}
          editTooltip="Chỉnh sửa khuyến mãi"
          deleteTooltip="Xóa khuyến mãi"
        />
      ),
    },
  ];

  useEffect(() => {
    fetchPromotions();
    fetchStatistics();
  }, [page, search, status, dateRange]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (search !== '') {
        fetchPromotions(1, search, status, dateRange);
        setPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  return (
    <PageContainer>
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        {/* Stats Cards */}
        <StatsGrid>
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={<stat.icon />}
              color={stat.color}
            />
          ))}
        </StatsGrid>

        {/* Promotion Management */}
        <Card style={{ marginBottom: 24 }}>
          <PageHeader
            title="Quản lý khuyến mãi"
            description="Tạo và quản lý các chương trình khuyến mãi"
            actions={
              <Space>
                <Button
                  onClick={() => {
                    fetchPromotions();
                    fetchStatistics();
                  }}
                  $variant="outline"
                >
                  <ReloadOutlined />
                  Làm mới
                </Button>
                <Button onClick={handleAdd} $variant="primary">
                  <PlusOutlined />
                  Thêm khuyến mãi
                </Button>
              </Space>
            }
          />

          <CardContent>
            {/* Filters Section */}
            <FiltersSection>
              <SearchBar
                placeholder="Tìm kiếm theo tên, mã khuyến mãi..."
                value={search}
                onChange={handleSearch}
              />

              <FilterGroup>
                <FilterLabel>Trạng thái:</FilterLabel>
                <SelectInput
                  value={status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <option value="">Tất cả</option>
                  <option value="active">Hoạt động</option>
                  <option value="inactive">Tạm dừng</option>
                  <option value="expired">Đã hết hạn</option>
                  <option value="upcoming">Sắp diễn ra</option>
                </SelectInput>
              </FilterGroup>

              <FilterGroup>
                <FilterLabel>Thời gian:</FilterLabel>
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  format="DD/MM/YYYY"
                  placeholder={['Từ ngày', 'Đến ngày']}
                  style={{ width: 250 }}
                />
              </FilterGroup>
            </FiltersSection>

            {/* Promotions Table */}
            <StyledTable>
              <AntTable
                columns={columns}
                dataSource={promotions}
                rowKey="id"
                loading={loading}
                pagination={false}
                scroll={{ x: 1400 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span
                          style={{
                            color: '#64748b',
                            fontFamily: "'Be Vietnam Pro', sans-serif",
                          }}
                        >
                          Không có dữ liệu
                        </span>
                      }
                    />
                  ),
                }}
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                }}
              />
            </StyledTable>

            {/* Pagination */}
            {total > 0 && (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginTop: 16,
                  padding: '16px 0',
                  borderTop: '1px solid #f0f0f0',
                }}
              >
                <span
                  style={{
                    color: '#64748b',
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                  }}
                >
                  Hiển thị {(page - 1) * PAGE_SIZE + 1} -{' '}
                  {Math.min(page * PAGE_SIZE, total)} của {total} khuyến mãi
                </span>
                <Pagination
                  current={page}
                  pageSize={PAGE_SIZE}
                  total={total}
                  onChange={(newPage) => {
                    setPage(newPage);
                    fetchPromotions(newPage, search, status, dateRange);
                  }}
                  showSizeChanger={false}
                  style={{
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                  }}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </Spin>

      {/* Promotion Modal */}
      <PromotionModal
        visible={modalVisible}
        onCancel={handleModalCancel}
        onSuccess={handleModalSuccess}
        editingPromotion={editingPromotion}
      />
    </PageContainer>
  );
};

export default PromotionManagement;
