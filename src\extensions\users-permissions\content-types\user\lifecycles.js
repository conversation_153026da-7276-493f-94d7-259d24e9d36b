const {
  baseAfterCreate,
  baseAfterUpdate,
  baseAfterDelete,
} = require('../../../../utils/base/lifecycles');

// Removed all functions and logic that use fields not present in the user schema:
// - createSubAccount function (uses: subBalance, numberOfSubAccount, sChildren, address, district, province, ward, level, fParent, verified, referCode)
// - afterUpdate logic (uses: level, colabStatus)

module.exports = {
  afterCreate(event) {
    baseAfterCreate(event);
  },
  async afterUpdate(event) {
    // Removed logic that uses non-existent fields (level, colabStatus)
    baseAfterUpdate(event);
  },
  afterDelete(event) {
    baseAfterDelete(event);
  },
};
