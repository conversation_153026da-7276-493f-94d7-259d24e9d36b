import React, { useState, useEffect } from 'react';
import { Table, Button, message, Switch } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useFetchClient } from '@strapi/helper-plugin';
import {
  PageContainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  ActionButtonGroup,
  ImageDisplay,
  StyledTable,
  CategoryBrandModal,
} from './shared';

const BrandManagement = () => {
  const [brands, setBrands] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingBrand, setEditingBrand] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [deleting, setDeleting] = useState(null);
  const { get, del } = useFetchClient();

  // Fetch brands
  const fetchBrands = async () => {
    setLoading(true);
    try {
      const response = await get('/management/products/brands');
      setBrands(response.data.data || []);
    } catch (error) {
      console.error('Error fetching brands:', error);
      message.error('Không thể tải danh sách thương hiệu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBrands();
  }, []);

  // Handle modal success
  const handleModalSuccess = () => {
    setModalVisible(false);
    setEditingBrand(null);
    fetchBrands();
  };

  // Handle delete brand
  const handleDelete = async (id) => {
    if (deleting === id) return; // Prevent double deletion

    setDeleting(id);
    try {
      await del(`/management/products/brands/${id}`);
      message.success('Xóa thương hiệu thành công');
      fetchBrands();
    } catch (error) {
      console.error('Error deleting brand:', error);
      message.error(
        error.response?.data?.message || 'Có lỗi xảy ra khi xóa thương hiệu'
      );
    } finally {
      setDeleting(null);
    }
  };

  // Handle edit
  const handleEdit = (brand) => {
    setEditingBrand(brand);
    setModalVisible(true);
  };

  // Handle add new
  const handleAdd = () => {
    setEditingBrand(null);
    setModalVisible(true);
  };

  // Filter brands based on search
  const filteredBrands = brands.filter(
    (brand) =>
      brand.name.toLowerCase().includes(searchText.toLowerCase()) ||
      (brand.description && brand.description.toLowerCase().includes(searchText.toLowerCase()))
  );

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'Logo',
      dataIndex: 'logo',
      key: 'logo',
      width: 100,
      render: (logo) => (
        <ImageDisplay
          src={logo?.url}
          alt="Brand Logo"
          size={50}
          placeholder="Không có"
          preview={true}
        />
      ),
    },
    {
      title: 'Tên thương hiệu',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      render: (description) => description || '-',
    },
    {
      title: 'Website',
      dataIndex: 'website',
      key: 'website',
      render: (website) =>
        website ? (
          <a href={website} target="_blank" rel="noopener noreferrer">
            {website}
          </a>
        ) : (
          '-'
        ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 120,
      render: (isActive) => (
        <Switch
          checked={isActive}
          disabled
          size="small"
          checkedChildren="Hoạt động"
          unCheckedChildren="Tạm dừng"
        />
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date) => new Date(date).toLocaleDateString('vi-VN'),
      sorter: (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <ActionButtonGroup
          onEdit={() => handleEdit(record)}
          onDelete={() => handleDelete(record.id)}
          deleteConfirmTitle="Xác nhận xóa"
          deleteConfirmDescription="Bạn có chắc chắn muốn xóa thương hiệu này?"
          showView={false}
          showEdit={true}
          showDelete={true}
          editTooltip="Chỉnh sửa thương hiệu"
          deleteTooltip="Xóa thương hiệu"
          deleteLoading={deleting === record.id}
          disabled={deleting !== null}
        />
      ),
    },
  ];

  return (
    <PageContainer style={{ background: '#f8fafc', minHeight: '100vh' }}>
      <Card>
        <PageHeader
          title="Quản lý thương hiệu"
          description="Xem và quản lý thương hiệu sản phẩm"
          actions={
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              size="large"
            >
              Thêm thương hiệu
            </Button>
          }
        />

        <CardContent>
          <FiltersSection>
            <SearchBar
              placeholder="Tìm kiếm thương hiệu..."
              value={searchText}
              onChange={setSearchText}
            />
          </FiltersSection>

          <StyledTable>
            <Table
              columns={columns}
              dataSource={filteredBrands}
              rowKey="id"
              loading={loading}
              pagination={{
                total: filteredBrands.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} của ${total} thương hiệu`,
              }}
              scroll={{ x: 800 }}
            />
          </StyledTable>
        </CardContent>
      </Card>

      <CategoryBrandModal
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingBrand(null);
        }}
        type="brand"
        editingItem={editingBrand}
        onSuccess={handleModalSuccess}
        showActiveSwitch={true}
      />
    </PageContainer>
  );
};

export default BrandManagement;
