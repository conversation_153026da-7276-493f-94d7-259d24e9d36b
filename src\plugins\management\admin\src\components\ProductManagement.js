import React, { useState, useEffect } from 'react';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Filter,
  Package,
  AlertCircle,
  CheckCircle2,
  ShoppingCart,
  Download,
  RefreshCw,
  HelpCircle,
  Save,
} from 'lucide-react';
import * as XLSX from 'xlsx';
import { useFetchClient } from '@strapi/helper-plugin';
import { useUpload } from '../hooks/useUpload';
import {
  Modal,
  message,
  Spin,
  Image,
  Table as AntTable,
  Empty,
  Button as AntButton,
  Row,
  Col,
  Drawer,
  Form,
  Input,
  Select,
  InputNumber,
  Tooltip,
  Space,
} from 'antd';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import {
  Page<PERSON>ontainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  StatsGrid,
  StatsCard,
  Button,
  FilterGroup,
  FilterLabel,
  DateInput,
  SelectInput,
  ActionButtonGroup,
  ImageDisplay,
  StyledTable,
  QuickAddModal,
  SharedImageUpload,
  FileUpload,
} from './shared';

import { createGlobalStyle } from 'styled-components';

// Global styles for ReactQuill and Upload
const QuillStyles = createGlobalStyle`
  .ql-editor {
    min-height: 250px;
    font-size: 14px;
    line-height: 1.6;
    font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  .ql-toolbar {
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: 1px solid #d9d9d9;
    background: #fafafa;
  }

  .ql-container {
    border: none;
    font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  .ql-editor.ql-blank::before {
    color: #bfbfbf;
    font-style: normal;
    font-family: 'Be Vietnam Pro', sans-serif;
  }

  /* Ensure proper styling for product description display */
  .product-description {
    font-family: 'Be Vietnam Pro', sans-serif;
    line-height: 1.6;
  }

  .product-description h1,
  .product-description h2,
  .product-description h3 {
    margin-top: 1em;
    margin-bottom: 0.5em;
    font-weight: 600;
  }

  .product-description p {
    margin-bottom: 1em;
  }

  .product-description ul,
  .product-description ol {
    margin-bottom: 1em;
    padding-left: 1.5em;
  }

  .product-description a {
    color: #2563eb;
    text-decoration: underline;
  }

  /* Custom Upload Button Styling */
  .ant-upload.ant-upload-select.ant-upload-select-picture-card {
    width: 102px !important;
    height: 102px !important;
    margin-inline-end: 8px !important;
    margin-bottom: 8px !important;
    text-align: center !important;
    vertical-align: top !important;
    background-color: rgba(0, 0, 0, 0.02) !important;
    border: 1px dashed #d9d9d9 !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    transition: border-color 0.3s !important;
  }

  .ant-upload.ant-upload-select.ant-upload-select-picture-card:hover {
    border-color: #1890ff !important;
  }

  .ant-upload.ant-upload-select.ant-upload-select-picture-card .ant-upload-text {
    font-size: 12px !important;
    color: #666 !important;
    margin-top: 8px !important;
  }
`;

// Styled Components (keeping only unique ones for ProductManagement)
import styled from 'styled-components';

const ProductInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const ProductName = styled.div`
  display: flex;
  flex-direction: column;

  p {
    font-weight: 500;
    color: #2563eb;
    cursor: pointer;
    margin: 0;
    transition: color 0.2s ease;

    &:hover {
      color: #1d4ed8;
    }
  }
`;

const CategoryBadge = styled.span`
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
`;

const StockText = styled.span`
  color: ${(props) => {
    switch (props.$color) {
      case 'text-red':
        return '#ef4444';
      case 'text-orange':
        return '#f59e0b';
      case 'text-green':
        return '#10b981';
      default:
        return '#374151';
    }
  }};
`;

const Switch = styled.label`
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
`;

const Slider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;

  &:before {
    position: absolute;
    content: '';
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
  }

  input:checked + & {
    background-color: #2563eb;
  }

  input:checked + &:before {
    transform: translateX(20px);
  }
`;

const PaginationContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
`;

const PaginationInfo = styled.div`
  font-size: 14px;
  color: #6b7280;
`;

const PaginationControls = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PaginationButton = styled.button`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #ffffff;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: #f9fafb;
    border-color: #9ca3af;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  ${(props) =>
    props.$active &&
    `
    background: #2563eb;
    color: #ffffff;
    border-color: #2563eb;
  `}
`;

const ProductManagement = () => {
  const { get, post, put, del } = useFetchClient();
  const { uploadMultipleFiles, uploadState } = useUpload();
  const [searchTerm, setSearchTerm] = useState('');
  const [products, setProducts] = useState([]);
  const [stats, setStats] = useState({
    totalProducts: 0,
    inStockProducts: 0,
    outOfStockProducts: 0,
    activeProducts: 0,
  });
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    pageCount: 0,
  });
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    status: '',
    dateFrom: '',
    dateTo: '',
  });
  const [selectedProducts, setSelectedProducts] = useState([]);

  // Quill editor configuration for product description
  const quillModules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ color: [] }, { background: [] }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ align: [] }],
      ['link'],
      ['clean'],
    ],
  };

  const quillFormats = [
    'header',
    'bold',
    'italic',
    'underline',
    'strike',
    'color',
    'background',
    'list',
    'bullet',
    'align',
    'link',
  ];

  // Product detail modal states
  const [productDetailVisible, setProductDetailVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [productViewers, setProductViewers] = useState([]);
  const [productFavorites, setProductFavorites] = useState([]);

  // Product edit modal states
  const [editProductVisible, setEditProductVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [categories, setCategories] = useState([]);
  const [brands, setBrands] = useState([]);
  const [uploadedImages, setUploadedImages] = useState([]);
  const [isAddMode, setIsAddMode] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    custom_collections: '',
    brand_id: '',
    purchase_price: 0,
    sales_price: 0,
    opening_stock: 0,
  });

  // Quick add category/brand modal states
  const [quickAddVisible, setQuickAddVisible] = useState(false);
  const [quickAddType, setQuickAddType] = useState('category');

  // Import products modal states
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importFile, setImportFile] = useState([]);
  const [importSubmitting, setImportSubmitting] = useState(false);

  // Fetch products from API
  const fetchProducts = async (resetFilters = false) => {
    try {
      setLoading(true);

      // Reset filters if requested
      if (resetFilters) {
        setFilters({
          search: '',
          category: '',
          status: '',
          dateFrom: '',
          dateTo: '',
        });
        setSearchTerm('');
        setPagination((prev) => ({ ...prev, page: 1 }));
      }

      const currentFilters = resetFilters
        ? {
            search: '',
            category: '',
            status: '',
            dateFrom: '',
            dateTo: '',
          }
        : filters;

      const queryParams = new URLSearchParams({
        page: resetFilters ? '1' : pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
        ...(currentFilters.search && { search: currentFilters.search }),
        ...(currentFilters.category && { category: currentFilters.category }),
        ...(currentFilters.status && { status: currentFilters.status }),
        ...(currentFilters.dateFrom && { dateFrom: currentFilters.dateFrom }),
        ...(currentFilters.dateTo && { dateTo: currentFilters.dateTo }),
      });

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 5000)
      );

      const response = await Promise.race([
        get(`/management/products?${queryParams}`),
        timeoutPromise,
      ]);

      if (response && response.data) {
        setProducts(response.data.data || []);
        setPagination((prev) => ({
          ...prev,
          total: response.data.meta?.pagination?.total || 0,
          pageCount: response.data.meta?.pagination?.pageCount || 0,
        }));
        setStats(response.data.meta?.stats || stats);
      } else {
        // No data received, use sample data
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount and when filters change
  useEffect(() => {
    // For development/demo purposes, you can uncomment the line below to use sample data directly
    // setSampleData();
    fetchProducts();
    // Also fetch categories and brands on mount
    fetchCategoriesAndBrands();
  }, [pagination.page, pagination.pageSize]);

  useEffect(() => {
    if (pagination.page === 1) {
      fetchProducts();
    } else {
      setPagination((prev) => ({ ...prev, page: 1 }));
    }
  }, [filters]);

  // Handle search
  useEffect(() => {
    setFilters((prev) => ({ ...prev, search: searchTerm }));
  }, [searchTerm]);

  // Handle select all
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedProducts(products.map((product) => product.id));
    } else {
      setSelectedProducts([]);
    }
  };

  // Clear selections when products change (e.g., pagination, filters)
  useEffect(() => {
    setSelectedProducts([]);
  }, [pagination.page, filters]);

  // Handle product status toggle
  const handleStatusToggle = async (productId, newStatus) => {
    try {
      await put(`/management/products/${productId}/status`, {
        isActive: newStatus,
      });
      message.success('Cập nhật trạng thái thành công');
      fetchProducts();
    } catch (error) {
      console.error('Error updating product status:', error);
      message.error('Không thể cập nhật trạng thái sản phẩm');
    }
  };

  // Handle product deletion
  const handleDelete = (productId) => {
    Modal.confirm({
      title: 'Xác nhận xóa sản phẩm',
      content: 'Bạn có chắc chắn muốn xóa sản phẩm này không?',
      okText: 'Xóa',
      cancelText: 'Hủy',
      okType: 'danger',
      onOk: async () => {
        try {
          await del(`/management/products/${productId}`);
          message.success('Xóa sản phẩm thành công');
          fetchProducts();
        } catch (error) {
          console.error('Error deleting product:', error);
          message.error('Không thể xóa sản phẩm');
        }
      },
    });
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedProducts.length === 0) {
      message.warning('Vui lòng chọn ít nhất một sản phẩm để xóa');
      return;
    }

    Modal.confirm({
      title: 'Xác nhận xóa sản phẩm',
      content: `Bạn có chắc chắn muốn xóa ${selectedProducts.length} sản phẩm đã chọn không?`,
      okText: 'Xóa',
      cancelText: 'Hủy',
      okType: 'danger',
      onOk: async () => {
        try {
          // Delete products in parallel
          await Promise.all(
            selectedProducts.map((productId) =>
              del(`/management/products/${productId}`)
            )
          );
          message.success(`Xóa thành công ${selectedProducts.length} sản phẩm`);
          setSelectedProducts([]);
          fetchProducts();
        } catch (error) {
          console.error('Error deleting products:', error);
          message.error('Không thể xóa một số sản phẩm');
        }
      },
    });
  };

  // Product detail handlers
  const handleProductClick = async (product) => {
    setSelectedProduct(product);
    try {
      // Fetch product viewers and favorites data
      // Note: These endpoints might not exist yet, so we'll handle gracefully
      const [viewersResponse, favoritesResponse] = await Promise.allSettled([
        get(`/management/products/${product.id}/viewers`),
        get(`/management/products/${product.id}/favorites`),
      ]);

      if (viewersResponse.status === 'fulfilled') {
        setProductViewers(viewersResponse.value.data || []);
      } else {
        setProductViewers([]);
      }

      if (favoritesResponse.status === 'fulfilled') {
        setProductFavorites(favoritesResponse.value.data || []);
      } else {
        setProductFavorites([]);
      }
    } catch (error) {
      console.error('Error fetching product details:', error);
      setProductViewers([]);
      setProductFavorites([]);
    }
    setProductDetailVisible(true);
  };

  // Product edit handlers
  const handleEditProduct = async (product) => {
    setEditingProduct(product);
    setIsAddMode(false);

    // Fetch categories and brands for the form
    await fetchCategoriesAndBrands();

    // Set form data state
    setFormData({
      name: product.name || '',
      description: product.mo_ta || '',
      custom_collections: product.danh_muc?.id || '',
      brand_id: product.thuong_hieu?.id || '',
      purchase_price: product.gia_goc || 0,
      sales_price: product.gia_ban || 0,
      opening_stock: product.so_luong_ton_kho || 0,
    });

    // Set uploaded images
    if (product.hinh_anh && product.hinh_anh.length > 0) {
      setUploadedImages(
        product.hinh_anh.map((img, index) => ({
          uid: img.id || index,
          name: img.name || `image-${index}`,
          status: 'done',
          url: img.url,
        }))
      );
    } else {
      setUploadedImages([]);
    }

    setEditProductVisible(true);
  };

  // Fetch categories and brands - reusable function (only active ones)
  const fetchCategoriesAndBrands = async () => {
    try {
      const [categoriesResponse, brandsResponse] = await Promise.allSettled([
        get('/management/products/categories?activeOnly=true'),
        get('/management/products/brands?activeOnly=true'),
      ]);

      if (categoriesResponse.status === 'fulfilled') {
        const categoriesData = categoriesResponse.value.data || [];
        // Handle both direct array and nested data structure
        const categoryList = Array.isArray(categoriesData)
          ? categoriesData
          : categoriesData.data || [];

        // Backend already filters active categories
        setCategories(categoryList);

        if (categoryList.length === 0) {
          message.warning(
            'Chưa có danh mục sản phẩm hoạt động nào. Vui lòng tạo hoặc kích hoạt danh mục trước.'
          );
        }
      } else {
        console.error('Categories fetch failed:', categoriesResponse.reason);
        message.error('Không thể tải danh mục sản phẩm');
        setCategories([]);
      }

      if (brandsResponse.status === 'fulfilled') {
        const brandsData = brandsResponse.value.data || [];
        // Handle both direct array and nested data structure
        const brandList = Array.isArray(brandsData)
          ? brandsData
          : brandsData.data || [];

        // Backend already filters active brands
        setBrands(brandList);

        if (brandList.length === 0) {
          message.warning(
            'Chưa có thương hiệu hoạt động nào. Vui lòng tạo hoặc kích hoạt thương hiệu trước.'
          );
        }
      } else {
        console.error('Brands fetch failed:', brandsResponse.reason);
        message.error('Không thể tải thương hiệu');
        setBrands([]);
      }
    } catch (error) {
      console.error('Error fetching categories and brands:', error);
      message.error('Lỗi khi tải danh mục và thương hiệu');
      setCategories([]);
      setBrands([]);
    }
  };

  // Helper functions
  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN').format(price);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('vi-VN').format(amount);
  };

  const getStockStatus = (stock) => {
    if (stock === 0) return { text: 'Hết hàng', color: 'text-red' };
    if (stock < 100) return { text: 'Sắp hết', color: 'text-orange' };
    return { text: 'Còn hàng', color: 'text-green' };
  };

  // Helper function to get product image URL
  const getProductImageUrl = (product) => {
    if (product.hinh_anh && product.hinh_anh.length > 0) {
      const firstImage = product.hinh_anh[0];
      // Prefer thumbnail for better performance, fallback to small, then original
      return (
        firstImage?.formats?.thumbnail?.url ||
        firstImage?.formats?.small?.url ||
        firstImage?.url ||
        '/placeholder.svg'
      );
    }
    return '/placeholder.svg';
  };

  // Handle add new product
  const handleAddProduct = async () => {
    setIsAddMode(true);
    setEditingProduct(null);

    // Fetch categories and brands for the form
    await fetchCategoriesAndBrands();

    // Reset form and images
    resetForm();
    setUploadedImages([]);
    setEditProductVisible(true);
  };

  // Reset form helper function
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      custom_collections: '',
      brand_id: '',
      purchase_price: 0,
      sales_price: 0,
      opening_stock: 0,
    });
  };

  // Handle submit (create/update product)
  const handleEditSubmit = async () => {
    try {
      setSubmitting(true);

      // Validate required fields
      if (
        !formData.name ||
        !formData.custom_collections ||
        !formData.sales_price
      ) {
        message.error('Vui lòng điền đầy đủ thông tin bắt buộc');
        setSubmitting(false);
        return;
      }

      if (uploadedImages.length === 0) {
        message.error('Vui lòng tải lên ít nhất một hình ảnh');
        setSubmitting(false);
        return;
      }

      // Prepare data for API
      const productData = {
        name: formData.name,
        mo_ta: formData.description,
        danh_muc: formData.custom_collections,
        thuong_hieu: formData.brand_id || null,
        gia_goc: formData.purchase_price,
        gia_ban: formData.sales_price,
        so_luong_ton_kho: formData.opening_stock,
        isActive: true,
        hot: false,
      };

      if (isAddMode) {
        // For creating new product, handle image upload with retry mechanism
        const imageIds = [];
        const filesToUpload = uploadedImages.filter(
          (file) => file.originFileObj
        );

        if (filesToUpload.length > 0) {
          try {
            const files = filesToUpload.map((file) => file.originFileObj);
            const { results, successCount } = await uploadMultipleFiles(files);

            if (successCount === 0) {
              message.error('Không thể tải lên hình ảnh. Vui lòng thử lại.');
              setSubmitting(false);
              return;
            }

            // Collect successful upload IDs
            results.forEach((result) => {
              if (result.success && result.data && result.data.length > 0) {
                imageIds.push(...result.data.map((img) => img.id));
              }
            });

            if (imageIds.length === 0) {
              message.error('Không thể tải lên hình ảnh. Vui lòng thử lại.');
              setSubmitting(false);
              return;
            }
          } catch (uploadError) {
            console.error('Error uploading images:', uploadError);
            message.error('Không thể tải lên hình ảnh. Vui lòng thử lại.');
            setSubmitting(false);
            return;
          }
        }

        // Create product with image IDs
        const finalData = {
          ...productData,
          hinh_anh: imageIds,
        };

        await post('/management/products', { data: finalData });
        message.success('Thêm sản phẩm thành công');
      } else {
        // Update existing product
        const imageIds = [];
        const filesToUpload = uploadedImages.filter(
          (file) => file.originFileObj
        );

        // Handle new image uploads
        if (filesToUpload.length > 0) {
          try {
            const files = filesToUpload.map((file) => file.originFileObj);
            const { results, successCount } = await uploadMultipleFiles(files);

            results.forEach((result) => {
              if (result.success && result.data && result.data.length > 0) {
                imageIds.push(...result.data.map((img) => img.id));
              }
            });
          } catch (uploadError) {
            console.error('Error uploading new images:', uploadError);
            message.error('Không thể tải lên hình ảnh mới');
            setSubmitting(false);
            return;
          }
        }

        // Keep existing images
        const existingImageIds = uploadedImages
          .filter((file) => !file.originFileObj && file.uid)
          .map((file) => file.uid);

        const allImageIds = [...existingImageIds, ...imageIds];

        const finalData = {
          ...productData,
          hinh_anh: allImageIds,
        };

        await put(`/management/products/${editingProduct?.id}`, {
          data: finalData,
        });
        message.success('Cập nhật sản phẩm thành công');
      }

      setEditProductVisible(false);
      resetForm();
      setUploadedImages([]);
      setIsAddMode(false);
      fetchProducts();
    } catch (error) {
      console.error('Error saving product:', error);
      message.error(
        isAddMode ? 'Không thể thêm sản phẩm' : 'Không thể cập nhật sản phẩm'
      );
    } finally {
      setSubmitting(false);
    }
  };

  const statsData = [
    {
      title: 'Tổng sản phẩm',
      value: stats.totalProducts.toString(),
      icon: Package,
      color: 'bg-blue',
    },
    {
      title: 'Sản phẩm còn hàng',
      value: stats.inStockProducts.toString(),
      icon: CheckCircle2,
      color: 'bg-blue-dark',
    },
    {
      title: 'Sản phẩm hết hàng',
      value: stats.outOfStockProducts.toString(),
      icon: AlertCircle,
      color: 'bg-green',
    },
    {
      title: 'Tổng sản phẩm đã bán',
      value: '0',
      icon: ShoppingCart,
      color: 'bg-red',
    },
  ];

  // Define columns for Antd Table
  const columns = [
    {
      title: 'Sản phẩm',
      key: 'product',
      width: 300,
      render: (_, product) => (
        <ProductInfo>
          <ImageDisplay
            src={getProductImageUrl(product)}
            alt={product.name}
            size={40}
            placeholder="Không có"
            preview={true}
            previewSrc={
              product.hinh_anh && product.hinh_anh.length > 0
                ? product.hinh_anh[0]?.formats?.large?.url ||
                  product.hinh_anh[0]?.formats?.medium?.url ||
                  product.hinh_anh[0]?.url ||
                  getProductImageUrl(product)
                : getProductImageUrl(product)
            }
          />
          <ProductName>
            <p onClick={() => handleProductClick(product)}>{product.name}</p>
          </ProductName>
        </ProductInfo>
      ),
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Giá bán',
      dataIndex: 'gia_ban',
      key: 'gia_ban',
      width: 120,
      render: (value) => formatPrice(value) || '-',
      sorter: (a, b) => (a.gia_ban || 0) - (b.gia_ban || 0),
    },
    {
      title: 'Giá gốc',
      dataIndex: 'gia_goc',
      key: 'gia_goc',
      width: 120,
      render: (value) => formatPrice(value) || '-',
      sorter: (a, b) => (a.gia_goc || 0) - (b.gia_goc || 0),
    },
    {
      title: 'Danh mục',
      key: 'category',
      width: 150,
      render: (_, product) => (
        <CategoryBadge>{product.danh_muc?.name || '-'}</CategoryBadge>
      ),
      sorter: (a, b) =>
        (a.danh_muc?.name || '').localeCompare(b.danh_muc?.name || ''),
    },
    {
      title: 'Thương hiệu',
      key: 'brand',
      width: 150,
      render: (_, product) => product.thuong_hieu?.name || '-',
      sorter: (a, b) =>
        (a.thuong_hieu?.name || '').localeCompare(b.thuong_hieu?.name || ''),
    },
    {
      title: 'Tồn kho',
      dataIndex: 'so_luong_ton_kho',
      key: 'so_luong_ton_kho',
      width: 100,
      render: (value) => {
        const stockStatus = getStockStatus(value);
        return (
          <StockText $color={stockStatus.color}>{formatPrice(value)}</StockText>
        );
      },
      sorter: (a, b) => (a.so_luong_ton_kho || 0) - (b.so_luong_ton_kho || 0),
    },
    {
      title: 'Lượt xem',
      dataIndex: 'luot_xem',
      key: 'luot_xem',
      width: 100,
      render: (value) => `${formatPrice(value || 0)} lượt`,
      sorter: (a, b) => (a.luot_xem || 0) - (b.luot_xem || 0),
    },
    {
      title: 'Lượt yêu thích',
      dataIndex: 'luot_yeu_thich',
      key: 'luot_yeu_thich',
      width: 120,
      render: (value) => formatPrice(value || 0),
      sorter: (a, b) => (a.luot_yeu_thich || 0) - (b.luot_yeu_thich || 0),
    },
    {
      title: 'Hoạt động',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (value, product) => (
        <Switch>
          <input
            type="checkbox"
            checked={value}
            onChange={(e) => handleStatusToggle(product.id, e.target.checked)}
          />
          <Slider />
        </Switch>
      ),
      sorter: (a, b) => Number(a.isActive) - Number(b.isActive),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString('vi-VN'),
      sorter: (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      render: (_, product) => (
        <ActionButtonGroup
          onView={() => handleProductClick(product)}
          onEdit={() => handleEditProduct(product)}
          onDelete={() => handleDelete(product.id)}
          deleteConfirmTitle="Xác nhận xóa"
          deleteConfirmDescription="Bạn có chắc chắn muốn xóa sản phẩm này?"
          showView={true}
          showEdit={true}
          showDelete={true}
          viewTooltip="Xem chi tiết sản phẩm"
          editTooltip="Chỉnh sửa sản phẩm"
          deleteTooltip="Xóa sản phẩm"
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <QuillStyles />
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        {/* Stats Cards */}
        <StatsGrid>
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={<stat.icon />}
              color={stat.color}
            />
          ))}
        </StatsGrid>

        {/* Product Management */}
        <Card>
          <PageHeader
            title="Danh sách sản phẩm"
            description={
              selectedProducts.length > 0
                ? `Đã chọn ${selectedProducts.length} sản phẩm`
                : 'Xem và quản lý danh sách sản phẩm'
            }
            actions={
              <>
                {selectedProducts.length > 0 && (
                  <Button
                    $variant="outline"
                    onClick={handleBulkDelete}
                    style={{ color: '#ef4444', borderColor: '#ef4444' }}
                  >
                    <Trash2 />
                    Xóa đã chọn ({selectedProducts.length})
                  </Button>
                )}
                <Button
                  $variant="outline"
                  onClick={() => fetchProducts(true)}
                  disabled={loading}
                >
                  <RefreshCw className={loading ? 'animate-spin' : ''} />
                  Làm mới
                </Button>
                <Button $variant="primary" onClick={handleAddProduct}>
                  <Plus />
                  Thêm mới sản phẩm
                </Button>
              </>
            }
          />

          <CardContent>
            {/* Search and Filters */}
            <FiltersSection>
              <SearchBar
                placeholder="Tìm kiếm sản phẩm..."
                value={searchTerm}
                onChange={setSearchTerm}
              />
              <FilterGroup>
                <FilterLabel>Ngày tạo từ:</FilterLabel>
                <DateInput
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) =>
                    setFilters((prev) => ({
                      ...prev,
                      dateFrom: e.target.value,
                    }))
                  }
                />
              </FilterGroup>
              <FilterGroup>
                <FilterLabel>Ngày tạo đến:</FilterLabel>
                <DateInput
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) =>
                    setFilters((prev) => ({ ...prev, dateTo: e.target.value }))
                  }
                />
              </FilterGroup>
              <FilterGroup>
                <FilterLabel>Chọn trạng thái:</FilterLabel>
                <SelectInput
                  value={filters.status}
                  onChange={(e) =>
                    setFilters((prev) => ({ ...prev, status: e.target.value }))
                  }
                >
                  <option value="">Tất cả</option>
                  <option value="true">Hoạt động</option>
                  <option value="false">Không hoạt động</option>
                </SelectInput>
              </FilterGroup>
            </FiltersSection>

            {/* Products Table */}
            <StyledTable>
              <AntTable
                columns={columns}
                dataSource={products}
                rowKey="id"
                loading={loading}
                pagination={false}
                scroll={{ x: 1200 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span
                          style={{
                            color: '#64748b',
                            fontFamily: "'Be Vietnam Pro', sans-serif",
                          }}
                        >
                          Không có dữ liệu
                        </span>
                      }
                    />
                  ),
                }}
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                }}
                rowSelection={{
                  selectedRowKeys: selectedProducts,
                  onChange: (selectedRowKeys) => {
                    setSelectedProducts(selectedRowKeys);
                  },
                  onSelectAll: (selected) => {
                    handleSelectAll(selected);
                  },
                }}
              />
            </StyledTable>

            {/* Pagination */}
            <PaginationContainer>
              <PaginationInfo>
                Hiển thị {(pagination.page - 1) * pagination.pageSize + 1} đến{' '}
                {Math.min(
                  pagination.page * pagination.pageSize,
                  pagination.total
                )}{' '}
                của {pagination.total} kết quả
              </PaginationInfo>
              <PaginationControls>
                <PaginationButton
                  disabled={pagination.page <= 1 || loading}
                  onClick={() =>
                    setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
                  }
                >
                  Trước
                </PaginationButton>
                <PaginationButton $active>{pagination.page}</PaginationButton>
                <PaginationButton
                  disabled={pagination.page >= pagination.pageCount || loading}
                  onClick={() =>
                    setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
                  }
                >
                  Sau
                </PaginationButton>
              </PaginationControls>
            </PaginationContainer>
          </CardContent>
        </Card>
      </Spin>

      {/* Product Detail Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 20 }}>
            <span style={{ fontWeight: 500, fontSize: 16, color: '#344054' }}>
              {selectedProduct?.name || 'Chi tiết sản phẩm'}
            </span>
          </div>
        }
        open={productDetailVisible}
        onCancel={() => setProductDetailVisible(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        {selectedProduct && (
          <div style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}>
            <Row gutter={[24, 24]}>
              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <h4 style={{ marginBottom: 8, color: '#374151' }}>
                    Hình ảnh sản phẩm
                  </h4>
                  {selectedProduct.hinh_anh &&
                  selectedProduct.hinh_anh.length > 0 ? (
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                      {selectedProduct.hinh_anh.map((img, index) => (
                        <Image
                          key={index}
                          src={img.url}
                          alt={`Product ${index + 1}`}
                          width={80}
                          height={80}
                          style={{ objectFit: 'cover', borderRadius: 4 }}
                        />
                      ))}
                    </div>
                  ) : (
                    <div
                      style={{
                        width: 80,
                        height: 80,
                        backgroundColor: '#f3f4f6',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: 4,
                        color: '#9ca3af',
                      }}
                    >
                      Không có
                    </div>
                  )}
                </div>

                <div style={{ marginBottom: 16 }}>
                  <h4 style={{ marginBottom: 8, color: '#374151' }}>
                    Thông tin cơ bản
                  </h4>
                  <div
                    style={{ display: 'flex', flexDirection: 'column', gap: 8 }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Tên sản phẩm:</span>
                      <span style={{ fontWeight: 500 }}>
                        {selectedProduct.name}
                      </span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>SKU:</span>
                      <span>{selectedProduct.sku || '-'}</span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Danh mục:</span>
                      <span>{selectedProduct.danh_muc?.name || '-'}</span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Thương hiệu:</span>
                      <span>{selectedProduct.thuong_hieu?.name || '-'}</span>
                    </div>
                  </div>
                </div>

                <div style={{ marginBottom: 16 }}>
                  <h4 style={{ marginBottom: 8, color: '#374151' }}>
                    Giá và tồn kho
                  </h4>
                  <div
                    style={{ display: 'flex', flexDirection: 'column', gap: 8 }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Giá bán:</span>
                      <span style={{ fontWeight: 500, color: '#059669' }}>
                        {formatCurrency(selectedProduct.gia_ban)} VNĐ
                      </span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Giá gốc:</span>
                      <span>{formatCurrency(selectedProduct.gia_goc)} VNĐ</span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Tồn kho:</span>
                      <span
                        style={{
                          color:
                            getStockStatus(selectedProduct.so_luong_ton_kho)
                              .color === 'text-red'
                              ? '#ef4444'
                              : getStockStatus(selectedProduct.so_luong_ton_kho)
                                  .color === 'text-orange'
                              ? '#f59e0b'
                              : '#10b981',
                        }}
                      >
                        {formatPrice(selectedProduct.so_luong_ton_kho)}
                      </span>
                    </div>
                  </div>
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <h4 style={{ marginBottom: 8, color: '#374151' }}>
                    Thống kê
                  </h4>
                  <div
                    style={{ display: 'flex', flexDirection: 'column', gap: 8 }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Lượt xem:</span>
                      <span>{formatPrice(selectedProduct.luot_xem || 0)}</span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Lượt yêu thích:</span>
                      <span>
                        {formatPrice(selectedProduct.luot_yeu_thich || 0)}
                      </span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Đã bán:</span>
                      <span>{formatPrice(selectedProduct.da_ban || 0)}</span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Trạng thái:</span>
                      <span
                        style={{
                          color: selectedProduct.isActive
                            ? '#059669'
                            : '#dc2626',
                          fontWeight: 500,
                        }}
                      >
                        {selectedProduct.isActive
                          ? 'Hoạt động'
                          : 'Không hoạt động'}
                      </span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Hot:</span>
                      <span
                        style={{
                          color: selectedProduct.hot ? '#dc2626' : '#6b7280',
                        }}
                      >
                        {selectedProduct.hot ? 'Có' : 'Không'}
                      </span>
                    </div>
                  </div>
                </div>

                <div style={{ marginBottom: 16 }}>
                  <h4 style={{ marginBottom: 8, color: '#374151' }}>
                    Ngày tạo/cập nhật
                  </h4>
                  <div
                    style={{ display: 'flex', flexDirection: 'column', gap: 8 }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>Ngày tạo:</span>
                      <span>
                        {new Date(selectedProduct.createdAt).toLocaleDateString(
                          'vi-VN'
                        )}
                      </span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span style={{ color: '#6b7280' }}>
                        Cập nhật lần cuối:
                      </span>
                      <span>
                        {new Date(selectedProduct.updatedAt).toLocaleDateString(
                          'vi-VN'
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>

            {selectedProduct.mo_ta && (
              <div style={{ marginTop: 24 }}>
                <h4 style={{ marginBottom: 8, color: '#374151' }}>
                  Mô tả sản phẩm
                </h4>
                <div
                  className="product-description"
                  style={{
                    padding: 16,
                    backgroundColor: '#f9fafb',
                    borderRadius: 8,
                    border: '1px solid #e5e7eb',
                    maxHeight: 200,
                    overflowY: 'auto',
                  }}
                  dangerouslySetInnerHTML={{ __html: selectedProduct.mo_ta }}
                />
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Product Edit Drawer */}
      <Drawer
        title={
          <span
            style={{
              fontWeight: 500,
              fontSize: 14,
              color: 'rgb(52, 64, 84)',
              fontFamily: 'Be Vietnam Pro, sans-serif',
            }}
          >
            {isAddMode ? 'Thêm sản phẩm' : 'Sửa sản phẩm'}
          </span>
        }
        placement="right"
        width="60%"
        open={editProductVisible}
        onClose={() => {
          setEditProductVisible(false);
          resetForm();
          setUploadedImages([]);
          setIsAddMode(false);
        }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <AntButton
              onClick={() => {
                setEditProductVisible(false);
                resetForm();
                setUploadedImages([]);
                setIsAddMode(false);
              }}
              style={{ marginRight: 8 }}
            >
              Hủy
            </AntButton>
            <AntButton
              type="primary"
              loading={submitting}
              onClick={handleEditSubmit}
            >
              {isAddMode ? 'Thêm sản phẩm' : 'Cập nhật'}
            </AntButton>
          </div>
        }
      >
        <div style={{ fontFamily: 'Be Vietnam Pro, sans-serif' }}>
          {/* Image Upload */}
          <div style={{ marginBottom: 24 }}>
            <label
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 8,
                marginBottom: 8,
              }}
            >
              Hình ảnh sản phẩm
              <Tooltip
                title={
                  <div>
                    <div>Dung lượng tối đa: 2MB mỗi file</div>
                    <div>Cho phép file định dạng sau: jpg, jpeg, png</div>
                    <div>Tối đa 5 hình ảnh</div>
                  </div>
                }
                placement="top"
              >
                <span style={{ color: 'rgb(72, 72, 71)', cursor: 'help' }}>
                  <HelpCircle size={14} />
                </span>
              </Tooltip>
            </label>
            <Form.Item>
              <SharedImageUpload
                value={uploadedImages}
                onChange={setUploadedImages}
                maxCount={5}
                accept="image/*"
                uploadText="Tải lên"
              />
            </Form.Item>
          </div>

          {/* Product Name */}
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 8 }}>
              Tên sản phẩm <span style={{ color: 'red' }}>*</span>
            </label>
            <input
              type="text"
              placeholder="Vui lòng nhập tên sản phẩm"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              style={{
                width: '100%',
                height: 40,
                borderRadius: 8,
                border: '1px solid #d9d9d9',
                padding: '0 12px',
                fontSize: 14,
                outline: 'none',
              }}
            />
          </div>

          {/* Category and Brand */}
          <Row gutter={16}>
            <Col span={12}>
              <div style={{ marginBottom: 16 }}>
                <label
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 8,
                    marginBottom: 8,
                  }}
                >
                  Danh mục <span style={{ color: 'red' }}>*</span>
                  <Tooltip title="Thêm danh mục mới" placement="top">
                    <AntButton
                      type="link"
                      size="small"
                      onClick={() => {
                        setQuickAddType('category');
                        setQuickAddVisible(true);
                      }}
                      style={{ padding: 0, height: 'auto', minWidth: 'auto' }}
                    >
                      <Plus size={14} />
                    </AntButton>
                  </Tooltip>
                </label>
                <Select
                  placeholder="Chọn danh mục"
                  value={formData.custom_collections || undefined}
                  onChange={(value) =>
                    setFormData({ ...formData, custom_collections: value })
                  }
                  style={{ width: '100%', height: 40 }}
                  allowClear
                >
                  {categories.map((category) => (
                    <Select.Option key={category.id} value={category.id}>
                      {category.name}
                    </Select.Option>
                  ))}
                </Select>
              </div>
            </Col>
            <Col span={12}>
              <div style={{ marginBottom: 16 }}>
                <label
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 8,
                    marginBottom: 8,
                  }}
                >
                  Thương hiệu
                  <Tooltip title="Thêm thương hiệu mới" placement="top">
                    <AntButton
                      type="link"
                      size="small"
                      onClick={() => {
                        setQuickAddType('brand');
                        setQuickAddVisible(true);
                      }}
                      style={{ padding: 0, height: 'auto', minWidth: 'auto' }}
                    >
                      <Plus size={14} />
                    </AntButton>
                  </Tooltip>
                </label>
                <Select
                  placeholder="Chọn thương hiệu"
                  value={formData.brand_id || undefined}
                  onChange={(value) =>
                    setFormData({ ...formData, brand_id: value })
                  }
                  style={{ width: '100%', height: 40 }}
                  allowClear
                >
                  {brands.map((brand) => (
                    <Select.Option key={brand.id} value={brand.id}>
                      {brand.name}
                    </Select.Option>
                  ))}
                </Select>
              </div>
            </Col>
          </Row>

          {/* Pricing */}
          <Row gutter={16}>
            <Col span={8}>
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 8 }}>
                  Giá gốc <span style={{ color: 'red' }}>*</span>
                </label>
                <InputNumber
                  placeholder="0"
                  value={formData.purchase_price}
                  onChange={(value) =>
                    setFormData({ ...formData, purchase_price: value || 0 })
                  }
                  style={{ width: '100%', height: 40 }}
                  min={0}
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
                />
              </div>
            </Col>
            <Col span={8}>
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 8 }}>
                  Giá bán <span style={{ color: 'red' }}>*</span>
                </label>
                <InputNumber
                  placeholder="0"
                  value={formData.sales_price}
                  onChange={(value) =>
                    setFormData({ ...formData, sales_price: value || 0 })
                  }
                  style={{ width: '100%', height: 40 }}
                  min={0}
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
                />
              </div>
            </Col>
            <Col span={8}>
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 8 }}>
                  Tồn kho ban đầu <span style={{ color: 'red' }}>*</span>
                </label>
                <InputNumber
                  placeholder="0"
                  value={formData.opening_stock}
                  onChange={(value) =>
                    setFormData({ ...formData, opening_stock: value || 0 })
                  }
                  style={{ width: '100%', height: 40 }}
                  min={0}
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
                />
              </div>
            </Col>
          </Row>

          {/* Description */}
          <div style={{ marginBottom: 80 }}>
            <label
              style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}
            >
              Mô tả sản phẩm
            </label>
            <div
              style={{
                border: '1px solid #d9d9d9',
                borderRadius: 8,
                overflow: 'hidden',
                backgroundColor: '#fff',
              }}
            >
              <ReactQuill
                theme="snow"
                value={formData.description}
                onChange={(content) =>
                  setFormData({ ...formData, description: content })
                }
                modules={quillModules}
                formats={quillFormats}
                placeholder="Vui lòng nhập mô tả sản phẩm..."
                style={{
                  height: '800px',
                  fontFamily: 'Be Vietnam Pro, sans-serif',
                }}
              />
            </div>
          </div>
        </div>
      </Drawer>

      {/* Quick Add Category/Brand Modal */}
      <QuickAddModal
        visible={quickAddVisible}
        onCancel={() => setQuickAddVisible(false)}
        type={quickAddType}
        onSuccess={fetchCategoriesAndBrands}
        showActiveSwitch={false}
        autoActive={true}
      />
    </PageContainer>
  );
};

export default ProductManagement;
