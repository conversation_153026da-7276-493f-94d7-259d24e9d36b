'use strict';

const promotionController = ({ strapi }) => ({
  async listPromotions(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('promotion-service')
      .listPromotions(ctx.query);
  },

  async createPromotion(ctx) {
    const { promotionData } = ctx.request.body;
    ctx.body = await strapi
      .plugin('management')
      .service('promotion-service')
      .createPromotion(promotionData);
  },

  async getPromotionDetail(ctx) {
    const { id } = ctx.params;

    // Validate ID parameter
    const promotionId = parseInt(id, 10);
    if (isNaN(promotionId) || promotionId <= 0) {
      ctx.throw(400, 'Invalid promotion ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('promotion-service')
      .getPromotionDetail(promotionId);
  },

  async updatePromotion(ctx) {
    const { id } = ctx.params;
    const { promotionData } = ctx.request.body;

    // Validate ID parameter
    const promotionId = parseInt(id, 10);
    if (isNaN(promotionId) || promotionId <= 0) {
      ctx.throw(400, 'Invalid promotion ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('promotion-service')
      .updatePromotion(promotionId, promotionData);
  },

  async deletePromotion(ctx) {
    const { id } = ctx.params;

    // Validate ID parameter
    const promotionId = parseInt(id, 10);
    if (isNaN(promotionId) || promotionId <= 0) {
      ctx.throw(400, 'Invalid promotion ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('promotion-service')
      .deletePromotion(promotionId);
  },

  async togglePromotionStatus(ctx) {
    const { id } = ctx.params;
    const { isActive } = ctx.request.body;

    // Validate ID parameter
    const promotionId = parseInt(id, 10);
    if (isNaN(promotionId) || promotionId <= 0) {
      ctx.throw(400, 'Invalid promotion ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('promotion-service')
      .togglePromotionStatus(promotionId, isActive);
  },

  async getPromotionStatistics(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('promotion-service')
      .getPromotionStatistics(ctx.query);
  },

  async validatePromotionCode(ctx) {
    const { code } = ctx.params;
    const { orderAmount, products } = ctx.query;
    ctx.body = await strapi
      .plugin('management')
      .service('promotion-service')
      .validatePromotionCode(code, {
        orderAmount: Number(orderAmount),
        products,
      });
  },

  async applyPromotion(ctx) {
    const { code, orderData } = ctx.request.body;
    ctx.body = await strapi
      .plugin('management')
      .service('promotion-service')
      .applyPromotion(code, orderData);
  },

  async getPromotionUsageHistory(ctx) {
    const { id } = ctx.params;

    // Validate ID parameter
    const promotionId = parseInt(id, 10);
    if (isNaN(promotionId) || promotionId <= 0) {
      ctx.throw(400, 'Invalid promotion ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('promotion-service')
      .getPromotionUsageHistory(promotionId, ctx.query);
  },

  async seedPromotions(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('promotion-service')
      .seedPromotions();
  },
});

module.exports = promotionController;
