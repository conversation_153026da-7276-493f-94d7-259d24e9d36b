{"kind": "collectionType", "collectionName": "audit_logs", "info": {"singularName": "audit-log", "pluralName": "audit-logs", "displayName": "<PERSON>t Log"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"contentType": {"type": "string", "required": true}, "action": {"type": "enumeration", "required": true, "enum": ["create", "update", "delete"]}, "result": {"type": "json", "required": false}, "author": {"type": "relation", "relation": "manyToOne", "target": "admin::user"}, "params": {"type": "json", "required": false}}}