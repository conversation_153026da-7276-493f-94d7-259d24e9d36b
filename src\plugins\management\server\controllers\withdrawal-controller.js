'use strict';

const withdrawalController = ({ strapi }) => ({
  async listWithdrawals(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('withdrawal-service')
      .listWithdrawals(ctx.query);
  },

  async getWithdrawalDetail(ctx) {
    const { id } = ctx.params;

    // Validate ID parameter
    const withdrawalId = parseInt(id, 10);
    if (isNaN(withdrawalId) || withdrawalId <= 0) {
      ctx.throw(400, 'Invalid withdrawal ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('withdrawal-service')
      .getWithdrawalDetail(withdrawalId);
  },

  async approveWithdrawal(ctx) {
    try {
      const { id } = ctx.params;
      const adminUser = ctx.state.user;

      // Validate ID parameter
      const withdrawalId = parseInt(id, 10);
      if (isNaN(withdrawalId) || withdrawalId <= 0) {
        ctx.throw(400, 'Invalid withdrawal ID');
        return;
      }

      const result = await strapi
        .plugin('management')
        .service('withdrawal-service')
        .approveWithdrawal(withdrawalId, adminUser);

      ctx.body = {
        success: true,
        data: result,
      };
    } catch (error) {
      ctx.throw(500, error.message);
    }
  },

  async rejectWithdrawal(ctx) {
    try {
      const { id } = ctx.params;
      const { adminNote } = ctx.request.body;
      const adminUser = ctx.state.user;

      // Validate ID parameter
      const withdrawalId = parseInt(id, 10);
      if (isNaN(withdrawalId) || withdrawalId <= 0) {
        ctx.throw(400, 'Invalid withdrawal ID');
        return;
      }

      const result = await strapi
        .plugin('management')
        .service('withdrawal-service')
        .rejectWithdrawal(withdrawalId, adminNote, adminUser);

      ctx.body = {
        success: true,
        data: result,
      };
    } catch (error) {
      ctx.throw(500, error.message);
    }
  },

  async getWithdrawalStatistics(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('withdrawal-service')
      .getWithdrawalStatistics(ctx.query);
  },
});

module.exports = withdrawalController;
