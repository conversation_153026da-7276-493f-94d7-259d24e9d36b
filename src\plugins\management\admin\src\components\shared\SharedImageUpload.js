import React, { useState } from 'react';
import { Upload, message, Progress } from 'antd';
import { Plus, UploadIcon, X, Wifi, WifiOff } from 'lucide-react';
import {
  useUpload,
  useNetworkCheck,
  UPLOAD_CONFIG,
} from '../../hooks/useUpload.js';

// Import CSS
import './SharedComponents.css';

// All styles are now in SharedComponents.css

const SharedImageUpload = ({
  value = [],
  onChange,
  maxCount = 1,
  accept = 'image/*',
  disabled = false,
  uploadText = 'Tải lên',
  beforeUpload,
}) => {
  const { validateFile } = useUpload();
  const { checkConnectivity } = useNetworkCheck();
  const handleUploadChange = (info) => {
    let { fileList } = info;

    // Ensure each file has the correct status and uid
    fileList = fileList.map((file) => {
      if (!file.uid) {
        file.uid = `upload-${Date.now()}-${Math.random()}`;
      }
      if (!file.status) {
        file.status = 'done';
      }
      return file;
    });

    // Add new files to existing files
    const existingFiles = value || [];
    const newFiles = [...existingFiles, ...fileList];

    // Limit the number of files if maxCount is set
    const limitedFiles = maxCount ? newFiles.slice(0, maxCount) : newFiles;

    onChange?.(limitedFiles);
  };

  const handleRemove = (fileToRemove) => {
    const existingFiles = value || [];
    const newFileList = existingFiles.filter(
      (file) => file.uid !== fileToRemove.uid
    );
    onChange?.(newFileList);
  };

  const defaultBeforeUpload = async (file) => {
    // Use centralized validation
    const validation = validateFile(file);
    if (!validation.valid) {
      message.error(validation.error);
      return false;
    }

    // Check network connectivity
    const isConnected = await checkConnectivity();
    if (!isConnected) {
      message.error(
        'Không có kết nối mạng. Vui lòng kiểm tra kết nối internet.'
      );
      return false;
    }

    return false; // Prevent auto upload, we handle it manually
  };

  const fileList = value || [];
  const showUploadButton = !maxCount || fileList.length < maxCount;

  return React.createElement(
    'div',
    { className: 'upload-container' },
    // Display uploaded images
    fileList.map((file) => {
      // Get image source for preview
      let imageSrc = '';

      if (file.url) {
        imageSrc = file.url;
      } else if (file.originFileObj) {
        imageSrc = URL.createObjectURL(file.originFileObj);
      }

      if (!imageSrc) {
        return null;
      }

      return React.createElement(
        'div',
        { key: file.uid, className: 'image-preview-container' },
        React.createElement('img', {
          src: imageSrc,
          alt: 'preview',
          style: { width: '100%', height: '100%', objectFit: 'cover' },
        }),
        !disabled &&
          React.createElement(
            'button',
            {
              className: 'remove-button',
              onClick: () => handleRemove(file),
              style: {
                position: 'absolute',
                top: '4px',
                right: '4px',
                width: '20px',
                height: '20px',
                background: 'rgba(0, 0, 0, 0.5)',
                border: 'none',
                borderRadius: '50%',
                color: 'white',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '12px',
                zIndex: 1,
              },
            },
            React.createElement(X, { size: 12 })
          )
      );
    }),
    // Upload button
    showUploadButton &&
      !disabled &&
      React.createElement(
        Upload,
        {
          listType: 'picture-card',
          fileList: [],
          onChange: handleUploadChange,
          beforeUpload: beforeUpload || defaultBeforeUpload,
          customRequest: ({ onSuccess }) => {
            // Custom request to prevent actual upload
            setTimeout(() => {
              onSuccess?.('ok');
            }, 0);
          },
          accept: accept,
          multiple: maxCount > 1,
          showUploadList: false,
          disabled: disabled,
        },
        React.createElement(
          'div',
          {
            className: 'upload-button',
            style: {
              width: '102px',
              height: '102px',
              backgroundColor: 'rgba(0, 0, 0, 0.02)',
              border: '1px dashed #d9d9d9',
              borderRadius: '4px',
              cursor: 'pointer',
              transition: 'border-color 0.3s',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              textAlign: 'center',
            },
          },
          React.createElement(UploadIcon, { size: 16 }),
          React.createElement(
            'div',
            {
              style: { fontSize: '12px', color: '#666', marginTop: '8px' },
            },
            uploadText
          )
        )
      )
  );
};

export default SharedImageUpload;
