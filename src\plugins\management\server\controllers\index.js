'use strict';

const myController = require('./my-controller');
const commissionController = require('./commission-controller');
const dashboardController = require('./dashboard-controller');
const newsController = require('./news-controller');
const orderController = require('./order-controller');
const productController = require('./product-controller');
const promotionController = require('./promotion-controller');
const settingsController = require('./settings-controller');
const userController = require('./user-controller');
const withdrawalController = require('./withdrawal-controller');

module.exports = {
  myController,
  'commission-controller': commissionController,
  'dashboard-controller': dashboardController,
  'news-controller': newsController,
  'order-controller': orderController,
  'product-controller': productController,
  'promotion-controller': promotionController,
  'settings-controller': settingsController,
  'user-controller': userController,
  'withdrawal-controller': withdrawalController,
};
