module.exports = {
  async baseAfterCreate(event) {
    const { result, params, action, model } = event;
    const { createdBy, updatedBy, ...rest } = result;

    try {
      // Create audit log entry
      await strapi.entityService.create('api::audit-log.audit-log', {
        data: {
          contentType: model.singularName,
          action,
          result: rest,
          author: createdBy?.id,
          params,
        },
      });
      console.log(
        `Audit log created: ${action} on ${model.singularName} with ID ${result.id}`
      );
    } catch (error) {
      console.error('Error creating audit log:', error);
      // Don't throw error to prevent breaking the main operation
    }
  },

  async baseAfterUpdate(event) {
    const { result, params, action, model } = event;
    const { createdBy, updatedBy, ...rest } = result;

    try {
      // Create audit log entry
      await strapi.entityService.create('api::audit-log.audit-log', {
        data: {
          contentType: model.singularName,
          action,
          result: rest,
          author: updatedBy?.id,
          params,
        },
      });
      console.log(
        `Audit log created: ${action} on ${model.singularName} with ID ${result.id}`
      );
    } catch (error) {
      console.error('Error creating audit log:', error);
      // Don't throw error to prevent breaking the main operation
    }
  },

  async baseAfterDelete(event) {
    const { result, params, action, model } = event;
    const { createdBy, updatedBy, ...rest } = result;

    try {
      // Create audit log entry
      await strapi.entityService.create('api::audit-log.audit-log', {
        data: {
          contentType: model.singularName,
          action,
          result: rest,
          author: updatedBy?.id,
          params,
        },
      });
      console.log(
        `Audit log created: ${action} on ${model.singularName} with ID ${result.id}`
      );
    } catch (error) {
      console.error('Error creating audit log:', error);
      // Don't throw error to prevent breaking the main operation
    }
  },
};
