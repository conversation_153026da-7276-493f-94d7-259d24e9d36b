'use strict';

const settingsController = ({ strapi }) => ({
  async getSettings(ctx) {
    try {
      console.log('🔍 Getting settings...');

      // Try to find existing settings first
      let settings = await strapi.db
        .query('api::thiet-lap-giao-dien.thiet-lap-giao-dien')
        .findOne({
          populate: ['logo'],
        });

      console.log('📄 Found settings:', settings);

      // If no settings exist, create default ones
      if (!settings) {
        console.log('🆕 Creating default settings...');
        settings = await strapi.db
          .query('api::thiet-lap-giao-dien.thiet-lap-giao-dien')
          .create({
            data: {
              title: 'LibertyHolding xin chào!',
              subtitle: 'Ngày mới tốt lành👋',
              colorType: 'solid',
              primaryColor: '#2563eb',
              gradientStart: '#667eea',
              gradientEnd: '#764ba2',
            },
            populate: ['logo'],
          });
        console.log('✅ Created settings:', settings);
      }

      ctx.body = {
        success: true,
        data: settings,
      };
    } catch (error) {
      console.error('❌ Error getting settings:', error);
      ctx.throw(500, 'Không thể tải cài đặt');
    }
  },

  async updateSettings(ctx) {
    try {
      const requestData = ctx.request.body;

      // Try to find existing settings
      let settings = await strapi.db
        .query('api::thiet-lap-giao-dien.thiet-lap-giao-dien')
        .findOne();

      if (settings) {
        // Update existing settings
        settings = await strapi.db
          .query('api::thiet-lap-giao-dien.thiet-lap-giao-dien')
          .update({
            where: { id: settings.id },
            data: requestData,
            populate: ['logo'],
          });
      } else {
        // Create new settings
        settings = await strapi.db
          .query('api::thiet-lap-giao-dien.thiet-lap-giao-dien')
          .create({
            data: requestData,
            populate: ['logo'],
          });
      }

      ctx.body = {
        success: true,
        data: settings,
      };
    } catch (error) {
      console.error('Error updating settings:', error);
      ctx.throw(500, 'Không thể lưu cài đặt');
    }
  },

  // Shipping settings methods
  async getShippingSettings(ctx) {
    try {
      console.log('🔍 Getting shipping settings...');

      // Try to find existing shipping settings first
      let shippingSettings = await strapi.db
        .query('api::cai-dat-van-chuyen.cai-dat-van-chuyen')
        .findOne();

      console.log('📄 Found shipping settings:', shippingSettings);

      // If no shipping settings exist, create default ones
      if (!shippingSettings) {
        console.log('🆕 Creating default shipping settings...');
        shippingSettings = await strapi.db
          .query('api::cai-dat-van-chuyen.cai-dat-van-chuyen')
          .create({
            data: {
              enableFreeShipping: false,
              freeShippingThreshold: 0,
              shippingFee: 0,
            },
          });
        console.log('✅ Created shipping settings:', shippingSettings);
      }

      ctx.body = {
        success: true,
        data: shippingSettings,
      };
    } catch (error) {
      console.error('❌ Error getting shipping settings:', error);
      ctx.throw(500, 'Không thể tải cài đặt vận chuyển');
    }
  },

  async updateShippingSettings(ctx) {
    try {
      console.log('💾 Updating shipping settings...');
      const requestData = ctx.request.body;
      console.log('📝 Request data:', requestData);

      // Try to find existing shipping settings
      let shippingSettings = await strapi.db
        .query('api::cai-dat-van-chuyen.cai-dat-van-chuyen')
        .findOne();

      if (shippingSettings) {
        // Update existing shipping settings
        shippingSettings = await strapi.db
          .query('api::cai-dat-van-chuyen.cai-dat-van-chuyen')
          .update({
            where: { id: shippingSettings.id },
            data: requestData,
          });
        console.log('✅ Updated shipping settings:', shippingSettings);
      } else {
        // Create new shipping settings
        shippingSettings = await strapi.db
          .query('api::cai-dat-van-chuyen.cai-dat-van-chuyen')
          .create({
            data: requestData,
          });
        console.log('✅ Created shipping settings:', shippingSettings);
      }

      ctx.body = {
        success: true,
        data: shippingSettings,
      };
    } catch (error) {
      console.error('❌ Error updating shipping settings:', error);
      ctx.throw(500, 'Không thể lưu cài đặt vận chuyển');
    }
  },

  // Zalo Mini App settings methods
  async getZaloMiniAppSettings(ctx) {
    try {
      console.log('🔍 Getting Zalo Mini App settings...');

      // Try to find existing Zalo Mini App settings first
      let zaloSettings = await strapi.db
        .query('api::cai-dat-zalo-mini-app.cai-dat-zalo-mini-app')
        .findOne();

      console.log('📄 Found Zalo Mini App settings:', zaloSettings);

      // If no settings exist, create default ones
      if (!zaloSettings) {
        console.log('🆕 Creating default Zalo Mini App settings...');
        zaloSettings = await strapi.db
          .query('api::cai-dat-zalo-mini-app.cai-dat-zalo-mini-app')
          .create({
            data: {
              oaId: '',
              appId: '',
              appSecret: '',
              checkoutPrivateKey: '',
            },
          });
        console.log('✅ Created Zalo Mini App settings:', zaloSettings);
      }

      ctx.body = {
        success: true,
        data: zaloSettings,
      };
    } catch (error) {
      console.error('❌ Error getting Zalo Mini App settings:', error);
      ctx.throw(500, 'Không thể tải cài đặt Zalo Mini App');
    }
  },

  async updateZaloMiniAppSettings(ctx) {
    try {
      console.log('💾 Updating Zalo Mini App settings...');
      const requestData = ctx.request.body;
      console.log('📝 Request data:', requestData);

      // Try to find existing Zalo Mini App settings
      let zaloSettings = await strapi.db
        .query('api::cai-dat-zalo-mini-app.cai-dat-zalo-mini-app')
        .findOne();

      if (zaloSettings) {
        // Update existing settings
        zaloSettings = await strapi.db
          .query('api::cai-dat-zalo-mini-app.cai-dat-zalo-mini-app')
          .update({
            where: { id: zaloSettings.id },
            data: requestData,
          });
        console.log('✅ Updated Zalo Mini App settings:', zaloSettings);
      } else {
        // Create new settings
        zaloSettings = await strapi.db
          .query('api::cai-dat-zalo-mini-app.cai-dat-zalo-mini-app')
          .create({
            data: requestData,
          });
        console.log('✅ Created Zalo Mini App settings:', zaloSettings);
      }

      ctx.body = {
        success: true,
        data: zaloSettings,
      };
    } catch (error) {
      console.error('❌ Error updating Zalo Mini App settings:', error);
      ctx.throw(500, 'Không thể lưu cài đặt Zalo Mini App');
    }
  },

  // Tax settings methods
  async getTaxSettings(ctx) {
    try {
      console.log('🔍 Getting tax settings...');

      // Try to find existing tax settings first
      let taxSettings = await strapi.db
        .query('api::cai-dat-thue.cai-dat-thue')
        .findOne();

      console.log('📄 Found tax settings:', taxSettings);

      // If no settings exist, create default ones
      if (!taxSettings) {
        console.log('🆕 Creating default tax settings...');
        taxSettings = await strapi.db
          .query('api::cai-dat-thue.cai-dat-thue')
          .create({
            data: {
              vatRate: 10,
            },
          });
        console.log('✅ Created tax settings:', taxSettings);
      }

      ctx.body = {
        success: true,
        data: taxSettings,
      };
    } catch (error) {
      console.error('❌ Error getting tax settings:', error);
      ctx.throw(500, 'Không thể tải cài đặt thuế');
    }
  },

  async updateTaxSettings(ctx) {
    try {
      console.log('💾 Updating tax settings...');
      const requestData = ctx.request.body;
      console.log('📝 Request data:', requestData);

      // Try to find existing tax settings
      let taxSettings = await strapi.db
        .query('api::cai-dat-thue.cai-dat-thue')
        .findOne();

      if (taxSettings) {
        // Update existing settings
        taxSettings = await strapi.db
          .query('api::cai-dat-thue.cai-dat-thue')
          .update({
            where: { id: taxSettings.id },
            data: requestData,
          });
        console.log('✅ Updated tax settings:', taxSettings);
      } else {
        // Create new settings
        taxSettings = await strapi.db
          .query('api::cai-dat-thue.cai-dat-thue')
          .create({
            data: requestData,
          });
        console.log('✅ Created tax settings:', taxSettings);
      }

      ctx.body = {
        success: true,
        data: taxSettings,
      };
    } catch (error) {
      console.error('❌ Error updating tax settings:', error);
      ctx.throw(500, 'Không thể lưu cài đặt thuế');
    }
  },

  // Commission settings methods
  async getCommissionSettings(ctx) {
    try {
      console.log('🔍 Getting commission settings...');

      // Try to find existing commission settings first
      let commissionSettings = await strapi.db
        .query('api::cai-dat-hoa-hong.cai-dat-hoa-hong')
        .findOne();

      console.log('📄 Found commission settings:', commissionSettings);

      // If no settings exist, return empty data
      if (!commissionSettings) {
        console.log('📭 No commission settings found, returning empty data');
        commissionSettings = {
          enableCommissionSystem: false,
          maxLevels: null,
          f1CommissionRate: null,
          f2CommissionRate: null,
          f3CommissionRate: null,
          f4CommissionRate: null,
          f5CommissionRate: null,
        };
      }

      ctx.body = {
        success: true,
        data: commissionSettings,
      };
    } catch (error) {
      console.error('❌ Error getting commission settings:', error);
      ctx.throw(500, 'Không thể tải cài đặt hoa hồng');
    }
  },

  async updateCommissionSettings(ctx) {
    try {
      console.log('💾 Updating commission settings...');
      const requestData = ctx.request.body;
      console.log('📝 Request data:', requestData);

      // Try to find existing commission settings
      let commissionSettings = await strapi.db
        .query('api::cai-dat-hoa-hong.cai-dat-hoa-hong')
        .findOne();

      if (commissionSettings) {
        // Update existing settings
        commissionSettings = await strapi.db
          .query('api::cai-dat-hoa-hong.cai-dat-hoa-hong')
          .update({
            where: { id: commissionSettings.id },
            data: requestData,
          });
        console.log('✅ Updated commission settings:', commissionSettings);
      } else {
        // Create new settings
        commissionSettings = await strapi.db
          .query('api::cai-dat-hoa-hong.cai-dat-hoa-hong')
          .create({
            data: requestData,
          });
        console.log('✅ Created commission settings:', commissionSettings);
      }

      ctx.body = {
        success: true,
        data: commissionSettings,
      };
    } catch (error) {
      console.error('❌ Error updating commission settings:', error);
      ctx.throw(500, 'Không thể lưu cài đặt hoa hồng');
    }
  },

  // Rank settings methods
  async getRankSettings(ctx) {
    try {
      console.log('🔍 Getting rank settings...');

      // Try to find existing rank settings first
      let rankSettings = await strapi.db
        .query('api::cai-dat-cap-bac.cai-dat-cap-bac')
        .findOne();

      console.log('📄 Found rank settings:', rankSettings);

      // If no settings exist, return empty data
      if (!rankSettings) {
        console.log('📭 No rank settings found, returning empty data');
        rankSettings = {
          ranks: [],
        };
      }

      ctx.body = {
        success: true,
        data: rankSettings,
      };
    } catch (error) {
      console.error('❌ Error getting rank settings:', error);
      ctx.throw(500, 'Không thể tải cài đặt cấp bậc');
    }
  },

  async updateRankSettings(ctx) {
    try {
      console.log('💾 Updating rank settings...');
      const requestData = ctx.request.body;
      console.log('📝 Request data:', requestData);

      // Try to find existing rank settings
      let rankSettings = await strapi.db
        .query('api::cai-dat-cap-bac.cai-dat-cap-bac')
        .findOne();

      if (rankSettings) {
        // Update existing settings
        rankSettings = await strapi.db
          .query('api::cai-dat-cap-bac.cai-dat-cap-bac')
          .update({
            where: { id: rankSettings.id },
            data: requestData,
          });
        console.log('✅ Updated rank settings:', rankSettings);
      } else {
        // Create new settings
        rankSettings = await strapi.db
          .query('api::cai-dat-cap-bac.cai-dat-cap-bac')
          .create({
            data: requestData,
          });
        console.log('✅ Created rank settings:', rankSettings);
      }

      ctx.body = {
        success: true,
        data: rankSettings,
      };
    } catch (error) {
      console.error('❌ Error updating rank settings:', error);
      ctx.throw(500, 'Không thể lưu cài đặt cấp bậc');
    }
  },

  // Products endpoint for rank settings
  async getProducts(ctx) {
    try {
      console.log('🔍 Getting products for rank settings...');

      const products = await strapi.entityService.findMany(
        'api::san-pham.san-pham',
        {
          limit: 1000,
          fields: ['id', 'name'],
          filters: {
            isActive: true,
          },
        }
      );

      console.log('📄 Found products:', products);

      ctx.body = {
        success: true,
        data: products || [],
      };
    } catch (error) {
      console.error('❌ Error getting products:', error);
      ctx.throw(500, 'Không thể tải danh sách sản phẩm');
    }
  },

  // Company settings methods
  async getCompanySettings(ctx) {
    try {
      console.log('🔍 Getting company settings...');

      // Try to find existing company settings first
      let companySettings = await strapi.db
        .query('api::cai-dat-cong-ty.cai-dat-cong-ty')
        .findOne();

      console.log('📄 Found company settings:', companySettings);

      // If no settings exist, return empty data
      if (!companySettings) {
        console.log('📭 No company settings found, returning empty data');
        companySettings = {
          companyName: '',
          companyAddress: '',
          companyPhone: '',
          companyEmail: '',
          taxCode: '',
          website: '',
        };
      }

      ctx.body = {
        success: true,
        data: companySettings,
      };
    } catch (error) {
      console.error('❌ Error getting company settings:', error);
      ctx.throw(500, 'Không thể tải cài đặt công ty');
    }
  },

  async updateCompanySettings(ctx) {
    try {
      console.log('💾 Updating company settings...');
      const requestData = ctx.request.body;
      console.log('📝 Request data:', requestData);

      // Try to find existing company settings
      let companySettings = await strapi.db
        .query('api::cai-dat-cong-ty.cai-dat-cong-ty')
        .findOne();

      if (companySettings) {
        // Update existing settings
        companySettings = await strapi.db
          .query('api::cai-dat-cong-ty.cai-dat-cong-ty')
          .update({
            where: { id: companySettings.id },
            data: requestData,
          });
        console.log('✅ Updated company settings:', companySettings);
      } else {
        // Create new settings
        companySettings = await strapi.db
          .query('api::cai-dat-cong-ty.cai-dat-cong-ty')
          .create({
            data: requestData,
          });
        console.log('✅ Created company settings:', companySettings);
      }

      ctx.body = {
        success: true,
        data: companySettings,
      };
    } catch (error) {
      console.error('❌ Error updating company settings:', error);
      ctx.throw(500, 'Không thể lưu cài đặt công ty');
    }
  },

  // Payment settings methods
  async getPaymentSettings(ctx) {
    try {
      console.log('🔍 Getting payment settings...');

      // Try to find existing payment settings first
      let paymentSettings = await strapi.db
        .query('api::cai-dat-thanh-toan.cai-dat-thanh-toan')
        .findOne();

      console.log('📄 Found payment settings:', paymentSettings);

      // If no settings exist, return empty data
      if (!paymentSettings) {
        console.log('📭 No payment settings found, returning empty data');
        paymentSettings = {
          enableCOD: true,
          enableBankTransfer: true,
          enableVNPay: false,
          bankName: '',
          bankAccount: '',
          bankOwner: '',
          vnpayMerchantId: '',
          vnpaySecretKey: '',
        };
      }

      ctx.body = {
        success: true,
        data: paymentSettings,
      };
    } catch (error) {
      console.error('❌ Error getting payment settings:', error);
      ctx.throw(500, 'Không thể tải cài đặt thanh toán');
    }
  },

  async updatePaymentSettings(ctx) {
    try {
      console.log('💾 Updating payment settings...');
      const requestData = ctx.request.body;
      console.log('📝 Request data:', requestData);

      // Try to find existing payment settings
      let paymentSettings = await strapi.db
        .query('api::cai-dat-thanh-toan.cai-dat-thanh-toan')
        .findOne();

      if (paymentSettings) {
        // Update existing settings
        paymentSettings = await strapi.db
          .query('api::cai-dat-thanh-toan.cai-dat-thanh-toan')
          .update({
            where: { id: paymentSettings.id },
            data: requestData,
          });
        console.log('✅ Updated payment settings:', paymentSettings);
      } else {
        // Create new settings
        paymentSettings = await strapi.db
          .query('api::cai-dat-thanh-toan.cai-dat-thanh-toan')
          .create({
            data: requestData,
          });
        console.log('✅ Created payment settings:', paymentSettings);
      }

      ctx.body = {
        success: true,
        data: paymentSettings,
      };
    } catch (error) {
      console.error('❌ Error updating payment settings:', error);
      ctx.throw(500, 'Không thể lưu cài đặt thanh toán');
    }
  },
});

module.exports = settingsController;
